# UI Components

This directory contains reusable UI components for the application.

## CircularRevealHeading

A circular loading component with text segments and image reveal on hover.

### Usage

```tsx
import { CircularRevealHeading } from "@/components/ui/circular-reveal-heading";

// Define the items for the circular reveal heading
const items = [
  {
    text: "ITEM ONE",
    image: "/path/to/image1.jpg"
  },
  {
    text: "ITEM TWO",
    image: "/path/to/image2.jpg"
  },
  {
    text: "ITEM THREE",
    image: "/path/to/image3.jpg"
  }
];

// Basic usage
<CircularRevealHeading 
  items={items}
  centerText={
    <div className="text-center">
      <h2 className="text-xl font-semibold">Title</h2>
      <p className="text-sm">Subtitle</p>
    </div>
  }
  size="md"
/>

// Available sizes: 'sm', 'md', 'lg'
```

## AppLoading

A wrapper component for CircularRevealHeading that provides predefined loading states for different sections of the application.

### Usage

```tsx
import { AppLoading } from "@/components/ui/app-loading";

// Default loading
<AppLoading />

// Restaurant loading
<AppLoading type="restaurant" />

// Table loading
<AppLoading type="table" />

// Menu loading
<AppLoading type="menu" />

// Custom loading
<AppLoading 
  type="default"
  title="Custom Title"
  subtitle="Custom subtitle text..."
  size="lg"
/>
```

### Props

- `type`: 'default' | 'restaurant' | 'table' | 'menu'
- `title`: Optional custom title
- `subtitle`: Optional custom subtitle
- `size`: 'sm' | 'md' | 'lg'
- `className`: Optional additional CSS classes
