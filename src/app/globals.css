@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-be-vietnam: var(--font-be-vietnam-pro);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: #fbfaf9;
  --foreground: #181510;
  --card: #f1edea;
  --card-foreground: #181510;
  --popover: #fbfaf9;
  --popover-foreground: #181510;
  --primary: #8a745c;
  --primary-foreground: #fbfaf9;
  --secondary: #f1edea;
  --secondary-foreground: #181510;
  --muted: #e2dcd4;
  --muted-foreground: #8a745c;
  --accent: #e5ccb2;
  --accent-foreground: #181510;
  --destructive: oklch(0.577 0.245 27.325);
  --border: #e2dcd4;
  --input: #e2dcd4;
  --ring: #8a745c;
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0.001 106.423);
  --sidebar-foreground: oklch(0.147 0.004 49.25);
  --sidebar-primary: oklch(0.216 0.006 56.043);
  --sidebar-primary-foreground: oklch(0.985 0.001 106.423);
  --sidebar-accent: oklch(0.97 0.001 106.424);
  --sidebar-accent-foreground: oklch(0.216 0.006 56.043);
  --sidebar-border: oklch(0.923 0.003 48.717);
  --sidebar-ring: oklch(0.709 0.01 56.259);

  /* Appearance settings variables */
  --color-primary: #8a745c;
  --color-secondary: #e5ccb2;
  --color-background: #fbfaf9;
  --color-text: #181510;
  --color-border: #e5e1dc;
  --font-size-scale: 1;
  --font-family: 'Be Vietnam Pro', sans-serif;
  --animation-duration: 0.3s;
  --transition-duration: 0.3s;
}

.dark {
  --background: #181510;
  --foreground: #fbfaf9;
  --card: #2a2520;
  --card-foreground: #fbfaf9;
  --popover: #2a2520;
  --popover-foreground: #fbfaf9;
  --primary: #8a745c;
  --primary-foreground: #fbfaf9;
  --secondary: #3a3530;
  --secondary-foreground: #fbfaf9;
  --muted: #3a3530;
  --muted-foreground: #a89a8c;
  --accent: #3a3530;
  --accent-foreground: #fbfaf9;
  --destructive: oklch(0.704 0.191 22.216);
  --border: #fbfaf9 / 10%;
  --input: #fbfaf9 / 15%;
  --ring: #8a745c;
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.216 0.006 56.043);
  --sidebar-foreground: oklch(0.985 0.001 106.423);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0.001 106.423);
  --sidebar-accent: oklch(0.268 0.007 34.298);
  --sidebar-accent-foreground: oklch(0.985 0.001 106.423);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.553 0.013 58.071);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-family);
    font-size: calc(1rem * var(--font-size-scale));
  }
  .font-be-vietnam {
    font-family: var(--font-be-vietnam);
  }
}

/* Accessibility classes */
.high-contrast {
  --color-text: #000000;
  --color-background: #ffffff;
  --foreground: #000000;
  --background: #ffffff;
}

.dark.high-contrast {
  --color-text: #ffffff;
  --color-background: #000000;
  --foreground: #ffffff;
  --background: #000000;
}

.compact-mode {
  --spacing-scale: 0.75;
}

.compact-mode .space-y-1 > * + * {
  margin-top: calc(0.25rem * var(--spacing-scale, 1));
}

.compact-mode .space-y-2 > * + * {
  margin-top: calc(0.5rem * var(--spacing-scale, 1));
}

.compact-mode .space-y-3 > * + * {
  margin-top: calc(0.75rem * var(--spacing-scale, 1));
}

.compact-mode .space-y-4 > * + * {
  margin-top: calc(1rem * var(--spacing-scale, 1));
}

.compact-mode .space-y-6 > * + * {
  margin-top: calc(1.5rem * var(--spacing-scale, 1));
}

.compact-mode .p-2 {
  padding: calc(0.5rem * var(--spacing-scale, 1));
}

.compact-mode .p-3 {
  padding: calc(0.75rem * var(--spacing-scale, 1));
}

.compact-mode .p-4 {
  padding: calc(1rem * var(--spacing-scale, 1));
}

.compact-mode .p-6 {
  padding: calc(1.5rem * var(--spacing-scale, 1));
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Custom transitions */
.transition-appearance {
  transition: background-color var(--transition-duration),
              color var(--transition-duration),
              border-color var(--transition-duration);
}
