'use client';

import { useTranslations } from 'next-intl';
import { useState } from 'react';
import Link from 'next/link';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Plus, Sparkles, Search } from 'lucide-react';

// Mock menu items
const initialMenuItems = [
  {
    id: '1',
    name: 'Margherita Pizza',
    description: 'Classic pizza with fresh basil, mozzarella, and tomato sauce.',
    price: 12.99,
    category: 'mainCourse',
    image: '/images/menu/margherita-pizza.jpg',
    available: true,
  },
  {
    id: '2',
    name: 'Caesar Salad',
    description: 'Romaine lettuce, croutons, Parmesan cheese, and Caesar dressing.',
    price: 9.99,
    category: 'appetizer',
    image: '/images/menu/caesar-salad.jpg',
    available: true,
  },
  {
    id: '3',
    name: 'Grilled Ribeye Steak',
    description: '12 oz ribeye steak grilled to perfection.',
    price: 24.99,
    category: 'mainCourse',
    image: '/images/menu/ribeye-steak.jpg',
    available: true,
  },
  {
    id: '4',
    name: 'Chocolate Lava Cake',
    description: 'Warm chocolate cake with a molten chocolate center.',
    price: 7.99,
    category: 'dessert',
    image: '/images/menu/chocolate-lava-cake.jpg',
    available: true,
  },
  {
    id: '5',
    name: 'Spaghetti Carbonara',
    description: 'Spaghetti with a creamy sauce, pancetta, and Parmesan cheese.',
    price: 14.99,
    category: 'mainCourse',
    image: '/images/menu/spaghetti-carbonara.jpg',
    available: true,
  },
];

export default function MenuPage() {
  const t = useTranslations('restaurant');
  const [menuItems, setMenuItems] = useState(initialMenuItems);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');

  // Filter menu items based on search term and active tab
  const filteredMenuItems = menuItems.filter(item => {
    // Filter by search term
    if (searchTerm && !item.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !item.description.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }

    // Filter by category
    if (activeTab !== 'all') {
      const categoryMap: Record<string, string> = {
        'Appetizers': 'appetizer',
        'Main Courses': 'mainCourse',
        'Desserts': 'dessert'
      };

      return item.category === categoryMap[activeTab];
    }

    return true;
  });

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div>
          <h1 className="text-[#181510] tracking-light text-[32px] font-bold leading-tight">Menu Items</h1>
        </div>
        <div className="flex items-center gap-3">
          <Link href="/app/restaurant/menu/ai-generate">
            <Button
              variant="outline"
              className="bg-[#e5ccb2] hover:bg-[#d6bd9e] text-[#181510] border-none"
            >
              <Sparkles className="mr-2 h-4 w-4" />
              AI Generate
            </Button>
          </Link>
          <Link href="/app/restaurant/menu/add">
            <Button className="bg-[#8a745c] hover:bg-[#6d5a48] text-white">
              <Plus className="mr-2 h-4 w-4" />
              Add Item
            </Button>
          </Link>
        </div>
      </div>

      <div className="mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8a745c] h-4 w-4" />
          <input
            placeholder="Search menu items"
            className="pl-10 bg-[#f9f7f5] border-[#e5e1dc] w-full h-10 rounded-lg border px-3 text-[#181510] focus:outline-none"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="mb-6">
        <TabsList className="bg-[#f1edea] p-1">
          <TabsTrigger
            value="all"
            className="data-[state=active]:bg-white data-[state=active]:text-[#181510] text-[#8a745c]"
          >
            All
          </TabsTrigger>
          <TabsTrigger
            value="Appetizers"
            className="data-[state=active]:bg-white data-[state=active]:text-[#181510] text-[#8a745c]"
          >
            Appetizers
          </TabsTrigger>
          <TabsTrigger
            value="Main Courses"
            className="data-[state=active]:bg-white data-[state=active]:text-[#181510] text-[#8a745c]"
          >
            Main Courses
          </TabsTrigger>
          <TabsTrigger
            value="Desserts"
            className="data-[state=active]:bg-white data-[state=active]:text-[#181510] text-[#8a745c]"
          >
            Desserts
          </TabsTrigger>
        </TabsList>
      </Tabs>

      <div className="bg-white rounded-lg border border-[#e5e1dc] overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-[#e5e1dc]">
                <th className="text-left p-4 font-medium text-[#181510]">Item</th>
                <th className="text-left p-4 font-medium text-[#181510]">Category</th>
                <th className="text-left p-4 font-medium text-[#181510]">Description</th>
                <th className="text-left p-4 font-medium text-[#181510]">Price</th>
                <th className="text-left p-4 font-medium text-[#181510]">Availability</th>
                <th className="text-left p-4 font-medium text-[#181510]">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredMenuItems.map((item) => (
                <tr key={item.id} className="border-b border-[#e5e1dc]">
                  <td className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="relative w-12 h-12 rounded-full overflow-hidden bg-[#f1edea]">
                        <div className="absolute inset-0 flex items-center justify-center text-[#8a745c]">
                          {item.name.charAt(0)}
                        </div>
                        <div
                          className="w-full h-full bg-center bg-cover"
                          style={{backgroundImage: `url("${item.image}")`}}
                        ></div>
                      </div>
                      <span className="font-medium text-[#181510]">{item.name}</span>
                    </div>
                  </td>
                  <td className="p-4 text-[#8a745c]">
                    {item.category === 'appetizer' ? 'Appetizers' :
                     item.category === 'mainCourse' ? 'Main Courses' :
                     item.category === 'dessert' ? 'Desserts' : item.category}
                  </td>
                  <td className="p-4 text-[#8a745c] max-w-xs">{item.description}</td>
                  <td className="p-4 text-[#181510]">${item.price.toFixed(2)}</td>
                  <td className="p-4">
                    <Badge
                      className={item.available
                        ? "bg-[#e5f2e5] text-[#2e7d32] hover:bg-[#d5e8d5] hover:text-[#2e7d32]"
                        : "bg-[#ffebee] text-[#c62828] hover:bg-[#ffe0e4] hover:text-[#c62828]"}
                    >
                      {item.available ? 'Available' : 'Unavailable'}
                    </Badge>
                  </td>
                  <td className="p-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-[#8a745c] hover:text-[#181510] hover:bg-[#f1edea]"
                    >
                      Edit
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
