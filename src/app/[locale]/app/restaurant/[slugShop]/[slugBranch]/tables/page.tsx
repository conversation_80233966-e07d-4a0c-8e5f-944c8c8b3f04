'use client';

import { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, QrCode, PlusCircle, Edit } from 'lucide-react';
import { getBranchWithShop } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';
import ReservationForm from '@/components/restaurant/ReservationForm';
import ReservationTab from '@/components/restaurant/ReservationTab';

// Mock table data
const mockTables = [
  {
    id: '1',
    number: 1,
    capacity: 4,
    status: 'available',
    location: 'dining',
    shape: 'square',
    positionX: 100,
    positionY: 100,
    width: 80,
    height: 80,
  },
  {
    id: '2',
    number: 2,
    capacity: 2,
    status: 'occupied',
    location: 'dining',
    shape: 'square',
    positionX: 200,
    positionY: 100,
    width: 60,
    height: 60,
  },
  {
    id: '3',
    number: 3,
    capacity: 6,
    status: 'reserved',
    location: 'dining',
    shape: 'rectangle',
    positionX: 300,
    positionY: 100,
    width: 120,
    height: 80,
  },
  {
    id: '4',
    number: 4,
    capacity: 4,
    status: 'available',
    location: 'dining',
    shape: 'square',
    positionX: 100,
    positionY: 200,
    width: 80,
    height: 80,
  },
  {
    id: '5',
    number: 5,
    capacity: 8,
    status: 'available',
    location: 'dining',
    shape: 'rectangle',
    positionX: 200,
    positionY: 200,
    width: 160,
    height: 80,
  },
  {
    id: '6',
    number: 6,
    capacity: 4,
    status: 'available',
    location: 'outdoor',
    shape: 'square',
    positionX: 100,
    positionY: 100,
    width: 80,
    height: 80,
  },
  {
    id: '7',
    number: 7,
    capacity: 2,
    status: 'occupied',
    location: 'outdoor',
    shape: 'square',
    positionX: 200,
    positionY: 100,
    width: 60,
    height: 60,
  },
  {
    id: '8',
    number: 8,
    capacity: 6,
    status: 'available',
    location: 'outdoor',
    shape: 'rectangle',
    positionX: 300,
    positionY: 100,
    width: 120,
    height: 80,
  },
];

interface TablesPageProps {
  params: {
    slugShop: string;
    slugBranch: string;
  };
}

export default function TablesPage({ params }: TablesPageProps) {
  const { slugShop, slugBranch } = params;
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);
  const [tables, setTables] = useState(mockTables);
  const [isAddReservationOpen, setIsAddReservationOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('floor-plan');
  const [selectedArea, setSelectedArea] = useState('dining');

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  // Filter tables by location
  const diningTables = tables.filter(table => table.location === 'dining');
  const outdoorTables = tables.filter(table => table.location === 'outdoor');
  const displayTables = selectedArea === 'dining' ? diningTables : outdoorTables;

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 border-green-300';
      case 'occupied':
        return 'bg-red-100 border-red-300';
      case 'reserved':
        return 'bg-yellow-100 border-yellow-300';
      default:
        return 'bg-gray-100 border-gray-300';
    }
  };

  const { branch, shop } = branchWithShop;

  return (
    <>
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/dashboard`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Tables</h1>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">
            Manage tables for {shop.name} - {branch.name}
          </p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="flex justify-between items-center border-b border-[#e2dcd4] mb-4">
          <TabsList className="bg-transparent h-auto">
            <TabsTrigger
              value="floor-plan"
              className="data-[state=active]:border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              Floor Plan
            </TabsTrigger>
            <TabsTrigger
              value="reservations"
              className="data-[state=active]:border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              Reservations
            </TabsTrigger>
            <TabsTrigger
              value="waitlist"
              className="data-[state=active]:border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              Waitlist
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="floor-plan" className="mt-0">
          <div className="bg-[#fbfaf9] rounded-lg border border-[#e5e1dc] overflow-hidden">
            <div className="p-4 border-b border-[#e5e1dc] flex justify-between items-center">
              <div className="flex items-center">
                <select
                  value={selectedArea}
                  onChange={(e) => setSelectedArea(e.target.value)}
                  className="bg-[#f1edea] text-[#181510] border-[#e2dcd4] rounded-md px-3 py-2 text-sm"
                >
                  <option value="dining">Dining Area</option>
                  <option value="outdoor">Outdoor Patio</option>
                </select>
              </div>

              <div className="flex justify-stretch">
                <div className="flex flex-1 gap-3 flex-wrap px-4 py-3 justify-end">
                  <Link href={`/app/restaurant/${slugShop}/${slugBranch}/tables/qr-codes`}>
                    <Button
                      className="bg-[#e58219] text-[#181510] hover:bg-[#d67917]"
                    >
                      <QrCode className="h-4 w-4 mr-2" />
                      Generate QR Codes
                    </Button>
                  </Link>
                  <Link href={`/app/restaurant/${slugShop}/${slugBranch}/tables/layout-editor`}>
                    <Button
                      className="bg-[#f1edea] text-[#181510] hover:bg-[#e2dcd4]"
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Edit Layout
                    </Button>
                  </Link>
                  <Dialog open={isAddReservationOpen} onOpenChange={setIsAddReservationOpen}>
                    <DialogTrigger asChild>
                      <Button className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]">
                        <PlusCircle className="h-4 w-4 mr-2" />
                        Add Reservation
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-3xl">
                      <DialogHeader>
                        <DialogTitle>Add New Reservation</DialogTitle>
                      </DialogHeader>
                      <ReservationForm
                        tables={tables.map(table => ({ id: table.id, number: table.number }))}
                        onSuccess={() => setIsAddReservationOpen(false)}
                        onCancel={() => setIsAddReservationOpen(false)}
                      />
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
            </div>

            <div className="p-6 relative" style={{ minHeight: '500px' }}>
              <div className="relative w-full h-full">
                {displayTables.map((table) => (
                  <Link
                    key={table.id}
                    href={`/app/restaurant/${slugShop}/${slugBranch}/tables/${table.id}`}
                    className={`absolute cursor-pointer border-2 ${getStatusColor(table.status)} rounded-md flex items-center justify-center transition-transform hover:scale-105`}
                    style={{
                      left: `${table.positionX}px`,
                      top: `${table.positionY}px`,
                      width: `${table.width}px`,
                      height: `${table.height}px`,
                    }}
                  >
                    <div className="text-center">
                      <div className="font-bold text-[#181510]">{table.number}</div>
                      <div className="text-xs text-[#8a745c]">{table.capacity} seats</div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="reservations" className="mt-0">
          <ReservationTab tables={tables} />
        </TabsContent>

        <TabsContent value="waitlist" className="mt-0">
          <div className="p-4 text-center text-[#8a745c]">
            <p>Waitlist feature coming soon</p>
          </div>
        </TabsContent>
      </Tabs>
    </>
  );
}
