'use client';

import { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft } from 'lucide-react';
import { getBranchWithShop } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';
import {
  useGetActiveOrdersQuery,
  useGetCompletedOrdersQuery
} from '@/lib/redux/api/endpoints/restaurantApi';
import React from 'react';

// Mock active orders
const mockActiveOrders = [
  {
    id: 'order-1',
    customer: '<PERSON>',
    items: [
      { id: 'item-1', name: 'Margherita Pizza', quantity: 1, price: 12.99 },
      { id: 'item-2', name: 'Caesar Salad', quantity: 1, price: 9.99 },
    ],
    total: 22.98,
    status: 'preparing',
    createdAt: '2023-06-15T14:30:00Z',
    table: 'Table 5',
  },
  {
    id: 'order-2',
    customer: '<PERSON>',
    items: [
      { id: 'item-3', name: 'Grilled Ribeye Steak', quantity: 2, price: 24.99 },
      { id: 'item-4', name: 'Chocolate Lava Cake', quantity: 2, price: 7.99 },
    ],
    total: 65.96,
    status: 'ready',
    createdAt: '2023-06-15T14:45:00Z',
    table: 'Table 3',
  },
  {
    id: 'order-3',
    customer: 'Bob Johnson',
    items: [
      { id: 'item-5', name: 'Spaghetti Carbonara', quantity: 1, price: 14.99 },
      { id: 'item-6', name: 'Caesar Salad', quantity: 1, price: 9.99 },
    ],
    total: 24.98,
    status: 'new',
    createdAt: '2023-06-15T15:00:00Z',
    table: 'Table 7',
  },
];

// Mock completed orders
const mockCompletedOrders = [
  {
    id: 'order-4',
    customer: 'Alice Brown',
    items: [
      { id: 'item-7', name: 'Margherita Pizza', quantity: 1, price: 12.99 },
      { id: 'item-8', name: 'Chocolate Lava Cake', quantity: 1, price: 7.99 },
    ],
    total: 20.98,
    status: 'completed',
    createdAt: '2023-06-15T13:30:00Z',
    completedAt: '2023-06-15T14:15:00Z',
    table: 'Table 2',
  },
  {
    id: 'order-5',
    customer: 'Charlie Wilson',
    items: [
      { id: 'item-9', name: 'Grilled Ribeye Steak', quantity: 1, price: 24.99 },
      { id: 'item-10', name: 'Caesar Salad', quantity: 1, price: 9.99 },
    ],
    total: 34.98,
    status: 'completed',
    createdAt: '2023-06-15T12:45:00Z',
    completedAt: '2023-06-15T13:30:00Z',
    table: 'Table 4',
  },
];

interface OrdersPageProps {
  params: {
    slugShop: string;
    slugBranch: string;
  };
}

export default function OrdersPage({ params }: OrdersPageProps) {
  const { slugShop, slugBranch } = params;
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('active');

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // In a real app, we would get the merchantId from context or auth
  const merchantId = "current-merchant-id";

  // Fetch orders data (using mock data for now)
  // const { data: activeOrders = mockActiveOrders, isLoading: isLoadingActive } = useGetActiveOrdersQuery(merchantId);
  // const { data: completedOrders = mockCompletedOrders, isLoading: isLoadingCompleted } = useGetCompletedOrdersQuery(merchantId);
  const activeOrders = mockActiveOrders;
  const completedOrders = mockCompletedOrders;

  // Format items for display
  const formatItems = (items) => {
    return items.map(item => `${item.quantity}x ${item.name}`).join(', ');
  };

  // Format time for display
  const formatTime = (dateString) => {
    if (!dateString) return '';
    return format(new Date(dateString), 'h:mm a');
  };

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  const { branch, shop } = branchWithShop;

  return (
    <>
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/dashboard`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 p-4">
        <div className="flex min-w-72 flex-col gap-3">
          <p className="text-[#181510] text-[32px] font-bold leading-tight">Orders</p>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">
            Manage incoming orders for {shop.name} - {branch.name}
          </p>
        </div>
      </div>

      <div className="pb-3">
        <div className="flex border-b border-[#e2dcd4] px-4 gap-8">
          <a
            className={`flex flex-col items-center justify-center border-b-[3px] ${
              activeTab === 'all'
                ? 'border-b-[#181510] text-[#181510]'
                : 'border-b-transparent text-[#8a745c]'
            } pb-[13px] pt-4`}
            href="#"
            onClick={(e) => {
              e.preventDefault();
              setActiveTab('all');
            }}
          >
            <p className="text-sm font-bold leading-normal tracking-[0.015em]">All Orders</p>
          </a>
          <a
            className={`flex flex-col items-center justify-center border-b-[3px] ${
              activeTab === 'active'
                ? 'border-b-[#181510] text-[#181510]'
                : 'border-b-transparent text-[#8a745c]'
            } pb-[13px] pt-4`}
            href="#"
            onClick={(e) => {
              e.preventDefault();
              setActiveTab('active');
            }}
          >
            <p className="text-sm font-bold leading-normal tracking-[0.015em]">Active</p>
          </a>
          <a
            className={`flex flex-col items-center justify-center border-b-[3px] ${
              activeTab === 'completed'
                ? 'border-b-[#181510] text-[#181510]'
                : 'border-b-transparent text-[#8a745c]'
            } pb-[13px] pt-4`}
            href="#"
            onClick={(e) => {
              e.preventDefault();
              setActiveTab('completed');
            }}
          >
            <p className="text-sm font-bold leading-normal tracking-[0.015em]">Completed</p>
          </a>
        </div>
      </div>

      {/* Active Orders Section */}
      {(activeTab === 'all' || activeTab === 'active') && (
        <>
          <h3 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Active Orders</h3>
          <div className="px-4 py-3 @container">
            <div className="flex overflow-hidden rounded-lg border border-[#e2dcd4] bg-[#fbfaf9]">
              <table className="flex-1">
                <thead>
                  <tr className="bg-[#fbfaf9]">
                    <th className="table-active-column-120 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">
                      Order ID
                    </th>
                    <th className="table-active-column-240 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">
                      Customer
                    </th>
                    <th className="table-active-column-360 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">
                      Items
                    </th>
                    <th className="table-active-column-480 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">
                      Total
                    </th>
                    <th className="table-active-column-600 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">
                      Status
                    </th>
                    <th className="table-active-column-720 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">
                      Time
                    </th>
                    <th className="table-active-column-840 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">
                      Table
                    </th>
                    <th className="table-active-column-960 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {activeOrders.map((order) => (
                    <tr key={order.id} className="border-t border-[#e2dcd4]">
                      <td className="table-active-column-120 px-4 py-3 text-[#181510] text-sm font-medium leading-normal">
                        {order.id}
                      </td>
                      <td className="table-active-column-240 px-4 py-3 text-[#181510] text-sm font-medium leading-normal">
                        {order.customer}
                      </td>
                      <td className="table-active-column-360 px-4 py-3 text-[#8a745c] text-sm font-normal leading-normal">
                        {formatItems(order.items)}
                      </td>
                      <td className="table-active-column-480 px-4 py-3 text-[#181510] text-sm font-medium leading-normal">
                        ${order.total.toFixed(2)}
                      </td>
                      <td className="table-active-column-600 px-4 py-3">
                        <Badge
                          className={`${
                            order.status === 'new'
                              ? 'bg-blue-100 text-blue-800 hover:bg-blue-100'
                              : order.status === 'preparing'
                              ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100'
                              : 'bg-green-100 text-green-800 hover:bg-green-100'
                          }`}
                        >
                          {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                        </Badge>
                      </td>
                      <td className="table-active-column-720 px-4 py-3 text-[#8a745c] text-sm font-normal leading-normal">
                        {formatTime(order.createdAt)}
                      </td>
                      <td className="table-active-column-840 px-4 py-3 text-[#8a745c] text-sm font-normal leading-normal">
                        {order.table}
                      </td>
                      <td className="table-active-column-960 px-4 py-3">
                        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/orders/${order.id}`}>
                          <Button variant="outline" size="sm" className="h-8 border-[#e2dcd4]">
                            View
                          </Button>
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </>
      )}

      {/* Completed Orders Section */}
      {(activeTab === 'all' || activeTab === 'completed') && (
        <>
          <h3 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Completed Orders</h3>
          <div className="px-4 py-3 @container">
            <div className="flex overflow-hidden rounded-lg border border-[#e2dcd4] bg-[#fbfaf9]">
              <table className="flex-1">
                <thead>
                  <tr className="bg-[#fbfaf9]">
                    <th className="table-completed-column-120 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">
                      Order ID
                    </th>
                    <th className="table-completed-column-240 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">
                      Customer
                    </th>
                    <th className="table-completed-column-360 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">
                      Items
                    </th>
                    <th className="table-completed-column-480 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">
                      Total
                    </th>
                    <th className="table-completed-column-600 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">
                      Completed At
                    </th>
                    <th className="table-completed-column-720 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">
                      Table
                    </th>
                    <th className="table-completed-column-840 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {completedOrders.map((order) => (
                    <tr key={order.id} className="border-t border-[#e2dcd4]">
                      <td className="table-completed-column-120 px-4 py-3 text-[#181510] text-sm font-medium leading-normal">
                        {order.id}
                      </td>
                      <td className="table-completed-column-240 px-4 py-3 text-[#181510] text-sm font-medium leading-normal">
                        {order.customer}
                      </td>
                      <td className="table-completed-column-360 px-4 py-3 text-[#8a745c] text-sm font-normal leading-normal">
                        {formatItems(order.items)}
                      </td>
                      <td className="table-completed-column-480 px-4 py-3 text-[#181510] text-sm font-medium leading-normal">
                        ${order.total.toFixed(2)}
                      </td>
                      <td className="table-completed-column-600 px-4 py-3 text-[#8a745c] text-sm font-normal leading-normal">
                        {formatTime(order.completedAt)}
                      </td>
                      <td className="table-completed-column-720 px-4 py-3 text-[#8a745c] text-sm font-normal leading-normal">
                        {order.table}
                      </td>
                      <td className="table-completed-column-840 px-4 py-3">
                        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/orders/${order.id}`}>
                          <Button variant="outline" size="sm" className="h-8 border-[#e2dcd4]">
                            View
                          </Button>
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </>
      )}
    </>
  );
}
