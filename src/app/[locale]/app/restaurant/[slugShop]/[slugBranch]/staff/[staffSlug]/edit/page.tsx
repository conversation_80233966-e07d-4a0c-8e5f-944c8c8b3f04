'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Link } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Save, X, Edit } from 'lucide-react';
import { AppLoading } from '@/components/ui/app-loading';
import { getBranchWithShop } from '@/mock/shopData';
import { generateSlug } from '@/lib/utils';
import { toast } from 'sonner';

// Mock staff data (same as in the detail page)
const mockStaffData = [
  {
    id: '1',
    name: '<PERSON>',
    slug: 'ethan-carter',
    role: 'Chef',
    status: 'active',
    schedule: 'Mon-Fri, 9am-5pm',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=1974&auto=format&fit=crop',
    bio: 'Experienced chef with over 10 years in fine dining. Specializes in modern American cuisine with a focus on locally sourced ingredients.',
    startDate: '2020-03-15',
    specialties: ['Modern American', 'Locally Sourced', 'Fine Dining'],
  },
  {
    id: '2',
    name: 'Olivia Bennett',
    slug: 'olivia-bennett',
    role: 'Server',
    status: 'active',
    schedule: 'Tue-Sat, 6pm-11pm',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?q=80&w=1974&auto=format&fit=crop',
    bio: 'Friendly and professional server with excellent customer service skills. Known for creating memorable dining experiences.',
    startDate: '2021-06-20',
    specialties: ['Customer Service', 'Wine Knowledge', 'Event Service'],
  },
  {
    id: '3',
    name: 'Noah Thompson',
    slug: 'noah-thompson',
    role: 'Bartender',
    status: 'active',
    schedule: 'Wed-Sun, 7pm-2am',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=1974&auto=format&fit=crop',
    bio: 'Creative mixologist with a passion for craft cocktails. Specializes in classic cocktails with modern twists.',
    startDate: '2019-11-10',
    specialties: ['Craft Cocktails', 'Mixology', 'Classic Drinks'],
  },
  {
    id: '4',
    name: 'Ava Harper',
    slug: 'ava-harper',
    role: 'Hostess',
    status: 'inactive',
    schedule: 'Mon-Fri, 5pm-10pm',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=1974&auto=format&fit=crop',
    bio: 'Welcoming hostess who ensures every guest feels valued from the moment they arrive.',
    startDate: '2022-01-05',
    specialties: ['Guest Relations', 'Reservation Management', 'Front of House'],
  },
  {
    id: '5',
    name: 'Liam Foster',
    slug: 'liam-foster',
    role: 'Manager',
    status: 'active',
    schedule: 'Mon-Sun, 10am-6pm',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=1974&auto=format&fit=crop',
    bio: 'Experienced restaurant manager with a focus on operational excellence and team development.',
    startDate: '2018-08-12',
    specialties: ['Operations Management', 'Team Leadership', 'Customer Experience'],
  },
];

interface StaffEditPageProps {
  params: {
    slugShop: string;
    slugBranch: string;
    staffSlug: string;
  };
}

export default function StaffEditPage({ params }: StaffEditPageProps) {
  const { slugShop, slugBranch, staffSlug } = params;
  const router = useRouter();
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Find the staff member by slug
  const staffMember = mockStaffData.find(staff => staff.slug === staffSlug);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    role: '',
    email: '',
    phone: '',
    username: '',
    password: '',
    permissions: [] as string[],
  });

  // Available permissions
  const availablePermissions = [
    'Manage Orders',
    'Update Menu',
    'View Reports',
    'Manage Staff'
  ];

  // Initialize form data when staff member is found
  useEffect(() => {
    if (staffMember) {
      setFormData({
        name: staffMember.name,
        role: staffMember.role,
        email: staffMember.email,
        phone: staffMember.phone,
        username: staffMember.slug, // Use slug as username
        password: '', // Don't pre-fill password
        permissions: ['Manage Orders'], // Default permissions
      });
    }
  }, [staffMember]);

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const togglePermission = (permission: string) => {
    setFormData(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permission)
        ? prev.permissions.filter(p => p !== permission)
        : [...prev.permissions, permission]
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate form
      if (!formData.name) {
        toast.error('Name is required');
        setIsSubmitting(false);
        return;
      }

      if (!formData.email) {
        toast.error('Email is required');
        setIsSubmitting(false);
        return;
      }

      if (!formData.role) {
        toast.error('Role is required');
        setIsSubmitting(false);
        return;
      }

      if (!formData.username) {
        toast.error('Username is required');
        setIsSubmitting(false);
        return;
      }

      // In a real app, we would call the API to update the staff member
      // const response = await updateStaff({ id: staffMember.id, data: formData }).unwrap();

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast.success('Staff member updated successfully');

      // Redirect back to staff detail page
      router.push(`/app/restaurant/${slugShop}/${slugBranch}/staff/${staffSlug}`);
    } catch (error) {
      toast.error('Failed to update staff member');
      console.error('Error updating staff member:', error);
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  if (!staffMember) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Staff
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Staff Member Not Found</h1>
          <p className="text-[#8a745c] text-sm">The staff member you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  const { branch, shop } = branchWithShop;

  return (
    <div className="font-be-vietnam bg-white min-h-screen">
      {/* Header */}
      <header className="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f4f2f0] px-10 py-3">
        <div className="flex items-center gap-4 text-[#181511]">
          <div className="size-4">
            <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M13.8261 17.4264C16.7203 18.1174 20.2244 18.5217 24 18.5217C27.7756 18.5217 31.2797 18.1174 34.1739 17.4264C36.9144 16.7722 39.9967 15.2331 41.3563 14.1648L24.8486 40.6391C24.4571 41.267 23.5429 41.267 23.1514 40.6391L6.64374 14.1648C8.00331 15.2331 11.0856 16.7722 13.8261 17.4264Z"
                fill="currentColor"
              ></path>
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M39.998 12.236C39.9944 12.2537 39.9875 12.2845 39.9748 12.3294C39.9436 12.4399 39.8949 12.5741 39.8346 12.7175C39.8168 12.7597 39.7989 12.8007 39.7813 12.8398C38.5103 13.7113 35.9788 14.9393 33.7095 15.4811C30.9875 16.131 27.6413 16.5217 24 16.5217C20.3587 16.5217 17.0125 16.131 14.2905 15.4811C12.0012 14.9346 9.44505 13.6897 8.18538 12.8168C8.17384 12.7925 8.16216 12.767 8.15052 12.7408C8.09919 12.6249 8.05721 12.5114 8.02977 12.411C8.00356 12.3152 8.00039 12.2667 8.00004 12.2612C8.00004 12.261 8 12.2607 8.00004 12.2612C8.00004 12.2359 8.0104 11.9233 8.68485 11.3686C9.34546 10.8254 10.4222 10.2469 11.9291 9.72276C14.9242 8.68098 19.1919 8 24 8C28.8081 8 33.0758 8.68098 36.0709 9.72276C37.5778 10.2469 38.6545 10.8254 39.3151 11.3686C39.9006 11.8501 39.9857 12.1489 39.998 12.236ZM4.95178 15.2312L21.4543 41.6973C22.6288 43.5809 25.3712 43.5809 26.5457 41.6973L43.0534 15.223C43.0709 15.1948 43.0878 15.1662 43.104 15.1371L41.3563 14.1648C43.104 15.1371 43.1038 15.1374 43.104 15.1371L43.1051 15.135L43.1065 15.1325L43.1101 15.1261L43.1199 15.1082C43.1276 15.094 43.1377 15.0754 43.1497 15.0527C43.1738 15.0075 43.2062 14.9455 43.244 14.8701C43.319 14.7208 43.4196 14.511 43.5217 14.2683C43.6901 13.8679 44 13.0689 44 12.2609C44 10.5573 43.003 9.22254 41.8558 8.2791C40.6947 7.32427 39.1354 6.55361 37.385 5.94477C33.8654 4.72057 29.133 4 24 4C18.867 4 14.1346 4.72057 10.615 5.94478C8.86463 6.55361 7.30529 7.32428 6.14419 8.27911C4.99695 9.22255 3.99999 10.5573 3.99999 12.2609C3.99999 13.1275 4.29264 13.9078 4.49321 14.3607C4.60375 14.6102 4.71348 14.8196 4.79687 14.9689C4.83898 15.0444 4.87547 15.1065 4.9035 15.1529C4.91754 15.1762 4.92954 15.1957 4.93916 15.2111L4.94662 15.223L4.95178 15.2312ZM35.9868 18.996L24 38.22L12.0131 18.996C12.4661 19.1391 12.9179 19.2658 13.3617 19.3718C16.4281 20.1039 20.0901 20.5217 24 20.5217C27.9099 20.5217 31.5719 20.1039 34.6383 19.3718C35.082 19.2658 35.5339 19.1391 35.9868 18.996Z"
                fill="currentColor"
              ></path>
            </svg>
          </div>
          <h2 className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em]">DineEase</h2>
        </div>
        <div className="flex flex-1 justify-end gap-8">
          <div className="flex items-center gap-9">
            <Link href={`/app/restaurant/${slugShop}/${slugBranch}/dashboard`} className="text-[#181511] text-sm font-medium leading-normal">Dashboard</Link>
            <Link href={`/app/restaurant/${slugShop}/${slugBranch}/orders`} className="text-[#181511] text-sm font-medium leading-normal">Orders</Link>
            <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu`} className="text-[#181511] text-sm font-medium leading-normal">Menu</Link>
            <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff`} className="text-[#181511] text-sm font-medium leading-normal">Staff</Link>
            <Link href={`/app/restaurant/${slugShop}/${slugBranch}/reports`} className="text-[#181511] text-sm font-medium leading-normal">Reports</Link>
          </div>
          <button className="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 bg-[#f4f2f0] text-[#181511] gap-2 text-sm font-bold leading-normal tracking-[0.015em] min-w-0 px-2.5">
            <div className="text-[#181511]" data-icon="Bell" data-size="20px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                <path d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-43.92,16-80a64,64,0,1,1,128,0c0,36.05,8.28,66.73,16,80Z"></path>
              </svg>
            </div>
          </button>
          <div
            className="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
            style={{backgroundImage: 'url("https://lh3.googleusercontent.com/aida-public/AB6AXuA2ogzQINv-Rk7KwrhlF0j3-PQWkCsNflerZz0zAA0jdy9Y295rorW48x37zDWnrCb6t9pc4BdyQ5z9KdCKB_B-AzrGlwg55Zt_bUCqBZ5_ohHCHjrwojsTMDkvqCQV4irjRFjwm9nKQNXYUIuVCul1pCwXU48SDeN9CNgoWOrUe_DbTZKlCLM8rfC3uUnEMOArvpvDINDToJlccolAicg4hB3NpSuiLD7gmXGtQyrxtqg-B_xV5Ky80rGbWUSU9IJsncwOyroCRgY-")'}}
          ></div>
        </div>
      </header>

      {/* Main Content */}
      <div className="px-40 flex flex-1 justify-center py-5">
        <div className="layout-content-container flex flex-col w-[512px] max-w-[512px] py-5 flex-1">
          <form onSubmit={handleSubmit}>
            {/* Title */}
            <div className="flex flex-wrap justify-between gap-3 p-4">
              <p className="text-[#181511] tracking-light text-[32px] font-bold leading-tight min-w-72">Edit Staff Member</p>
            </div>

            {/* Full Name */}
            <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
              <label className="flex flex-col min-w-40 flex-1">
                <p className="text-[#181511] text-base font-medium leading-normal pb-2">Full Name</p>
                <Input
                  placeholder="Enter full name"
                  className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
                  value={formData.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                  required
                />
              </label>
            </div>

            {/* Role */}
            <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
              <label className="flex flex-col min-w-40 flex-1">
                <p className="text-[#181511] text-base font-medium leading-normal pb-2">Role</p>
                <Select value={formData.role} onValueChange={(value) => handleChange('role', value)}>
                  <SelectTrigger className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal">
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="chef">Chef</SelectItem>
                    <SelectItem value="server">Server</SelectItem>
                    <SelectItem value="bartender">Bartender</SelectItem>
                    <SelectItem value="hostess">Hostess</SelectItem>
                    <SelectItem value="manager">Manager</SelectItem>
                    <SelectItem value="cashier">Cashier</SelectItem>
                  </SelectContent>
                </Select>
              </label>
            </div>

            {/* Contact Number */}
            <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
              <label className="flex flex-col min-w-40 flex-1">
                <p className="text-[#181511] text-base font-medium leading-normal pb-2">Contact Number</p>
                <Input
                  placeholder="Enter contact number"
                  className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
                  value={formData.phone}
                  onChange={(e) => handleChange('phone', e.target.value)}
                />
              </label>
            </div>

            {/* Email Address */}
            <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
              <label className="flex flex-col min-w-40 flex-1">
                <p className="text-[#181511] text-base font-medium leading-normal pb-2">Email Address</p>
                <Input
                  type="email"
                  placeholder="Enter email address"
                  className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
                  value={formData.email}
                  onChange={(e) => handleChange('email', e.target.value)}
                  required
                />
              </label>
            </div>

            {/* Username */}
            <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
              <label className="flex flex-col min-w-40 flex-1">
                <p className="text-[#181511] text-base font-medium leading-normal pb-2">Username</p>
                <Input
                  placeholder="Enter username"
                  className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
                  value={formData.username}
                  onChange={(e) => handleChange('username', e.target.value)}
                  required
                />
              </label>
            </div>

            {/* Password */}
            <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
              <label className="flex flex-col min-w-40 flex-1">
                <p className="text-[#181511] text-base font-medium leading-normal pb-2">Password</p>
                <Input
                  type="password"
                  placeholder="Enter new password (leave blank to keep current)"
                  className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
                  value={formData.password}
                  onChange={(e) => handleChange('password', e.target.value)}
                />
              </label>
            </div>

            {/* Permissions */}
            <h3 className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Permissions</h3>
            <div className="flex gap-3 p-3 flex-wrap pr-4">
              {availablePermissions.map((permission) => (
                <button
                  key={permission}
                  type="button"
                  onClick={() => togglePermission(permission)}
                  className={`flex h-8 shrink-0 items-center justify-center gap-x-2 rounded-full pl-4 pr-4 transition-colors ${
                    formData.permissions.includes(permission)
                      ? 'bg-[#e58219] text-white'
                      : 'bg-[#f4f2f0] text-[#181511] hover:bg-[#e5e1dc]'
                  }`}
                >
                  <p className="text-sm font-medium leading-normal">{permission}</p>
                </button>
              ))}
            </div>

            {/* Submit Button */}
            <div className="flex px-4 py-3 justify-end">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#e58219] hover:bg-[#d4741a] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em]"
              >
                <span className="truncate">
                  {isSubmitting ? 'Saving...' : 'Save Changes'}
                </span>
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
