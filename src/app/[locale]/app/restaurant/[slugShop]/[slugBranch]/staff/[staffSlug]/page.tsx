'use client';

import { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Mail, Phone, Calendar, Clock, Edit } from 'lucide-react';
import { AppLoading } from '@/components/ui/app-loading';
import { getBranchWithShop } from '@/mock/shopData';

// Mock staff data (same as in the list page)
const mockStaffData = [
  {
    id: '1',
    name: '<PERSON>',
    slug: 'ethan-carter',
    role: 'Chef',
    status: 'active',
    schedule: 'Mon-Fri, 9am-5pm',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=1974&auto=format&fit=crop',
    bio: 'Experienced chef with over 10 years in fine dining. Specializes in modern American cuisine with a focus on locally sourced ingredients.',
    startDate: '2020-03-15',
    specialties: ['Modern American', 'Locally Sourced', 'Fine Dining'],
  },
  {
    id: '2',
    name: '<PERSON>',
    slug: 'olivia-bennett',
    role: 'Server',
    status: 'active',
    schedule: 'Tue-Sat, 6pm-11pm',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?q=80&w=1974&auto=format&fit=crop',
    bio: 'Friendly and professional server with excellent customer service skills. Known for creating memorable dining experiences.',
    startDate: '2021-06-20',
    specialties: ['Customer Service', 'Wine Knowledge', 'Event Service'],
  },
  {
    id: '3',
    name: 'Noah Thompson',
    slug: 'noah-thompson',
    role: 'Bartender',
    status: 'active',
    schedule: 'Wed-Sun, 7pm-2am',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=1974&auto=format&fit=crop',
    bio: 'Creative mixologist with a passion for craft cocktails. Specializes in classic cocktails with modern twists.',
    startDate: '2019-11-10',
    specialties: ['Craft Cocktails', 'Mixology', 'Classic Drinks'],
  },
  {
    id: '4',
    name: 'Ava Harper',
    slug: 'ava-harper',
    role: 'Hostess',
    status: 'inactive',
    schedule: 'Mon-Fri, 5pm-10pm',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=1974&auto=format&fit=crop',
    bio: 'Welcoming hostess who ensures every guest feels valued from the moment they arrive.',
    startDate: '2022-01-05',
    specialties: ['Guest Relations', 'Reservation Management', 'Front of House'],
  },
  {
    id: '5',
    name: 'Liam Foster',
    slug: 'liam-foster',
    role: 'Manager',
    status: 'active',
    schedule: 'Mon-Sun, 10am-6pm',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=1974&auto=format&fit=crop',
    bio: 'Experienced restaurant manager with a focus on operational excellence and team development.',
    startDate: '2018-08-12',
    specialties: ['Operations Management', 'Team Leadership', 'Customer Experience'],
  },
];

interface StaffDetailPageProps {
  params: {
    slugShop: string;
    slugBranch: string;
    staffSlug: string;
  };
}

export default function StaffDetailPage({ params }: StaffDetailPageProps) {
  const { slugShop, slugBranch, staffSlug } = params;
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);

  // Find the staff member by slug
  const staffMember = mockStaffData.find(staff => staff.slug === staffSlug);

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  if (!staffMember) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Staff
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Staff Member Not Found</h1>
          <p className="text-[#8a745c] text-sm">The staff member you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  const { branch, shop } = branchWithShop;

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Staff
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">{staffMember.name}</h1>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">
            {staffMember.role} at {shop.name} - {branch.name}
          </p>
        </div>
        <div className="flex items-end">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff/${staffSlug}/edit`}>
            <Button className="bg-[#8a745c] hover:bg-[#6d5a48] text-white">
              <Edit className="mr-2 h-4 w-4" />
              Edit Staff Member
            </Button>
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Card */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="text-[#181510]">Profile</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col items-center text-center">
              <img
                src={staffMember.image}
                alt={staffMember.name}
                className="w-24 h-24 rounded-full object-cover mb-4"
              />
              <h3 className="text-[#181510] text-lg font-semibold">{staffMember.name}</h3>
              <p className="text-[#8a745c] text-sm">{staffMember.role}</p>
              <Badge
                variant={staffMember.status === 'active' ? 'default' : 'secondary'}
                className={staffMember.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}
              >
                {staffMember.status}
              </Badge>
            </div>

            <div className="space-y-3 pt-4">
              <div className="flex items-center gap-3">
                <Mail className="h-4 w-4 text-[#8a745c]" />
                <span className="text-[#181510] text-sm">{staffMember.email}</span>
              </div>
              <div className="flex items-center gap-3">
                <Phone className="h-4 w-4 text-[#8a745c]" />
                <span className="text-[#181510] text-sm">{staffMember.phone}</span>
              </div>
              <div className="flex items-center gap-3">
                <Calendar className="h-4 w-4 text-[#8a745c]" />
                <span className="text-[#181510] text-sm">Started: {new Date(staffMember.startDate).toLocaleDateString()}</span>
              </div>
              <div className="flex items-center gap-3">
                <Clock className="h-4 w-4 text-[#8a745c]" />
                <span className="text-[#181510] text-sm">{staffMember.schedule}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Details Card */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="text-[#181510]">Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h4 className="text-[#181510] font-semibold mb-2">Bio</h4>
              <p className="text-[#8a745c] text-sm leading-relaxed">{staffMember.bio}</p>
            </div>

            <div>
              <h4 className="text-[#181510] font-semibold mb-2">Specialties</h4>
              <div className="flex flex-wrap gap-2">
                {staffMember.specialties.map((specialty, index) => (
                  <Badge key={index} variant="outline" className="border-[#e2dcd4] text-[#8a745c]">
                    {specialty}
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
