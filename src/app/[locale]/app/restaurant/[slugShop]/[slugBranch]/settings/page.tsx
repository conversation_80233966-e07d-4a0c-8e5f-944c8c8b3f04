'use client';

import React, { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowLeft, User, Bell, CreditCard, Globe, Shield, Palette, HelpCircle } from 'lucide-react';
import { getBranchWithShop } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';

interface SettingsPageProps {
  params: {
    slugShop: string;
    slugBranch: string;
  };
}

interface SettingCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  href: string;
}

const SettingCard = ({ icon, title, description, href }: SettingCardProps) => (
  <Link href={href} className="block transition-transform hover:scale-[1.02]">
    <Card className="bg-[#fbfaf9] border-[#e5e1dc] h-full">
      <CardContent className="p-6 flex items-start gap-4">
        <div className="bg-[#f1edea] p-3 rounded-lg">
          {icon}
        </div>
        <div>
          <h3 className="text-[#181510] font-bold mb-1">{title}</h3>
          <p className="text-[#8a745c] text-sm">{description}</p>
        </div>
      </CardContent>
    </Card>
  </Link>
);

export default function SettingsPage({ params }: SettingsPageProps) {
  const { slugShop, slugBranch } = params;
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  const { branch, shop } = branchWithShop;

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/dashboard`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Settings</h1>
          <p className="text-[#8a745c] text-sm">Configure your account and restaurant settings for {shop.name} - {branch.name}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <SettingCard
          icon={<User className="h-6 w-6 text-[#8a745c]" />}
          title="Profile"
          description="Manage your personal information and account settings"
          href={`/app/restaurant/${slugShop}/${slugBranch}/settings/profile`}
        />

        <SettingCard
          icon={<Bell className="h-6 w-6 text-[#8a745c]" />}
          title="Notifications"
          description="Configure your notification preferences"
          href={`/app/restaurant/${slugShop}/${slugBranch}/settings/notifications`}
        />

        <SettingCard
          icon={<CreditCard className="h-6 w-6 text-[#8a745c]" />}
          title="Billing & Subscription"
          description="Manage your billing information and subscription plan"
          href={`/app/restaurant/${slugShop}/${slugBranch}/settings/billing`}
        />

        <SettingCard
          icon={<Globe className="h-6 w-6 text-[#8a745c]" />}
          title="Language & Region"
          description="Set your preferred language and regional settings"
          href={`/app/restaurant/${slugShop}/${slugBranch}/settings/language`}
        />

        <SettingCard
          icon={<Shield className="h-6 w-6 text-[#8a745c]" />}
          title="Security"
          description="Manage your account security and privacy settings"
          href={`/app/restaurant/${slugShop}/${slugBranch}/settings/security`}
        />

        <SettingCard
          icon={<Palette className="h-6 w-6 text-[#8a745c]" />}
          title="Appearance"
          description="Customize the look and feel of your restaurant dashboard"
          href={`/app/restaurant/${slugShop}/${slugBranch}/settings/appearance`}
        />

        <SettingCard
          icon={<HelpCircle className="h-6 w-6 text-[#8a745c]" />}
          title="Help & Support"
          description="Get help and support for your restaurant management"
          href={`/app/restaurant/${slugShop}/${slugBranch}/help`}
        />
      </div>
    </div>
  );
}
