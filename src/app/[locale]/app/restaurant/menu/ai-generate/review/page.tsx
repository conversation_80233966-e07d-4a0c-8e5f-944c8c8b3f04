'use client';

import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import styles from './review.module.css';
import EditItemModal from './components/EditItemModal';

// Mock data for generated menu items
const mockGeneratedMenuItems = [
  {
    id: '1',
    name: 'Spaghetti Carbonara',
    description: 'Classic pasta dish with creamy sauce, pancetta, and Parmesan cheese.',
    price: 18.99,
    category: 'Main Course',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBitRJJIAwrsT3sN69YFrCfepOrR3pneJ4inWpwR7iu2tNMseZE90YE_oCNgkmq5Gs_UyUyocZX_daSu7NymBhRzfJNJRLDw3DRHhJ3HgXSSPZMyBTglltX4eAZaQ3guLhNiLPkYbwwmj77cLZogMsBiBm-6X4wKpxb-20L1PlcjmWmV_OTwZFlsVy_Q-52Bw-nvA7xF1H6egrCjRuyBkQz_H9jh_Ooe-9XgXYdUb8yvdgkJPgPXxmjpmHDFdx4aHichBxyNhdEo8WW',
  },
  {
    id: '2',
    name: 'Margherita Pizza',
    description: 'Traditional pizza with tomato sauce, mozzarella, and fresh basil.',
    price: 15.99,
    category: 'Main Course',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCBxlonv4RXd0k1X6nNlnWz7HkK7tda0fTHRZKhsEdu4AhkJns9RpHwe5FzAzrGhEvv2gb69h6Pq6u-KcZTO0eItX-tlQatn-ulW0_Vf_QqXLCcC2nlE_Z7AWrpHtjG5EUnSlY7_9rFRcgdfKfRFhYxE_aHcsHUaISRqE9Axj0a1kqkb-NqZk8d-O8j8XCxh_1uPHf2RXJxJYtcZaoM2CBW3TAbMM-HHZJwlm_nlU6GiXw30wPVmjriOvfDiz0O_A0QZBSJm8jVjpYv',
  },
  {
    id: '3',
    name: 'Tiramisu',
    description: 'Coffee-flavored dessert with layers of mascarpone cream and cocoa.',
    price: 9.99,
    category: 'Dessert',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAVXkcgopl7C3R5rhVVK87k708mcOJDX1X3B9GJGfJQibNh4QJOFwf3L_gyHP-EJFOpauOSpWhnjVZjkCQZH4l2ZLG7cm1AMWeP735xdYsvrqoEjaVum4t_WPAGx7DC2ePvvdrfLSn21MhdzPa02Iw-HrlJbSRHyddMWBvgPxnLwf_xh9vbbBB2xWAfk50ikquhZ0o7BmgxSjw-pi8pn2Q0uEkwqVulcXsCYVdTnhKmeun-XuuQBOIiglpoDE9DIwhYXgHcjSG2r_kq',
  },
  {
    id: '4',
    name: 'Lasagna Bolognese',
    description: 'Layered pasta with meat sauce, béchamel, and cheese.',
    price: 22.99,
    category: 'Main Course',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAh8eYEyYXcyDpyWFKDDznvVgHbZvZ4yZ2oYryS_xXIjWO4btMh2mDn-5wf0PfxySVEtgu4fDWbtVP7Dp3NLEH0Q6eGsxYDhzxM2JSjRERtf_KWk8l__iWRPfEdVXGcLiMbV6VZX9BUQAPwi0IsyFMZxyxV0NBKpRSeVusBrtb8SAlPvpOAdw8mlFF7jfZLgj2DT2QQ0YQQ1EZeB1XBtXk0TUYnxEzUxzEhSCz6YQyD8xRa8e51R2p_Xw7zSTIkOPfivki3ZcqSe_Aw',
  },
  {
    id: '5',
    name: 'Caprese Salad',
    description: 'Simple salad with fresh tomatoes, mozzarella, and basil.',
    price: 12.99,
    category: 'Appetizer',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBc_5w7kE0XNZCk4Pq4jqkUxZdRoKkepiOpjfNL5BIi01dp6yZjD50AVlFE2lqFwrRtOF8PAFT9drxY9JdynacoLa6ZEON6w1B75S4Kg-pnruigJm0TnHxMFTcNm1zkuerGKYM-yhLgnFrDKMAGnD2MaH2Kb6_-ljQhXJiAl8wyOfSRrlNSs8zV5Z-XgYJflJD5EiQuiJbDMfkGyI4ZY4zs3BUSZ-MyVgWgTvG2YcsZSsXcxSpY8qUpAut93DiaQoMQnmfKhWyPLAQS',
  },
];

// Mock menu data
const mockMenuData = {
  name: 'Italian Delights',
  description: 'A curated menu of classic Italian dishes, featuring fresh pasta, wood-fired pizzas, and decadent desserts.',
  image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCZgGze0Vhx8mYs0dq2YS4AKyibNA8awWekisf0Ml2x4DDg0vMUXfpKKOwd9jRs-T8kfhBof5f2lVsqU36HoZVhq-Of4Fs_NfjRtuoQ9tOZOh0aaHrLZsJLEog5Or_L2YkUFuv3W-ZKaxtixQ7QBmwnCTiXjqTzHmrgbADM0dMbUNhhNWwKZGT9_oBhPoXyhDsIb3WJvrSbCFnQB6L0yQAlDFaaJS6BoNjqij0PPlnXtnKAMStzneVVdUBHU2fdOOJzc6pKIIWZe8u0',
};

export default function ReviewMenuPage() {
  const t = useTranslations('restaurant');
  const router = useRouter();

  // State for menu items
  const [menuItems, setMenuItems] = useState(mockGeneratedMenuItems);
  const [menuData, setMenuData] = useState(mockMenuData);
  const [isPublishing, setIsPublishing] = useState(false);

  // Handle publishing menu
  const handlePublishMenu = () => {
    setIsPublishing(true);

    // Simulate API call delay
    setTimeout(() => {
      // In a real implementation, this would call an API to publish the menu
      alert('Menu published successfully!');
      setIsPublishing(false);
      router.push('/app/restaurant/menu');
    }, 1500);
  };

  // State for edit modal
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [currentEditItem, setCurrentEditItem] = useState<typeof mockGeneratedMenuItems[0] | null>(null);

  // Handle edit item
  const handleEditItem = (itemId: string) => {
    const itemToEdit = menuItems.find(item => item.id === itemId);
    if (itemToEdit) {
      setCurrentEditItem(itemToEdit);
      setIsEditModalOpen(true);
    }
  };

  // Handle save edited item
  const handleSaveEditedItem = (editedItem: typeof mockGeneratedMenuItems[0]) => {
    setMenuItems(menuItems.map(item =>
      item.id === editedItem.id ? editedItem : item
    ));
  };

  return (
    <>
      <div className="flex flex-wrap justify-between gap-3 p-4">
        <div className="flex min-w-72 flex-col gap-3">
          <p className="text-[#181511] tracking-light text-[32px] font-bold leading-tight">Review and Edit Menu</p>
          <p className="text-[#887663] text-sm font-normal leading-normal">Generated from uploaded image. Please review and finalize before publishing.</p>
        </div>
      </div>

      <h3 className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Menu Preview</h3>
      <div className="p-4 @container">
        <div className="flex flex-col items-stretch justify-start rounded-xl @xl:flex-row @xl:items-start">
          <div
            className="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl"
            style={{backgroundImage: `url("${menuData.image}")`}}
          ></div>
          <div className="flex w-full min-w-72 grow flex-col items-stretch justify-center gap-1 py-4 @xl:px-4">
            <p className="text-[#887663] text-sm font-normal leading-normal">AI Generated Menu</p>
            <p className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em]">{menuData.name}</p>
            <div className="flex items-end gap-3 justify-between">
              <p className="text-[#887663] text-base font-normal leading-normal">
                {menuData.description}
              </p>
            </div>
          </div>
        </div>
      </div>

      <h3 className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Menu Items</h3>
      <div className="px-4 py-3 @container">
        <div className="flex overflow-hidden rounded-xl border border-[#e5e1dc] bg-white">
          <table className="flex-1">
            <thead>
              <tr className="bg-white">
                <th className="table-column-120 px-4 py-3 text-left text-[#181511] w-[400px] text-sm font-medium leading-normal">Item</th>
                <th className="table-column-240 px-4 py-3 text-left text-[#181511] w-[400px] text-sm font-medium leading-normal">
                  Description
                </th>
                <th className="table-column-360 px-4 py-3 text-left text-[#181511] w-[400px] text-sm font-medium leading-normal">Price</th>
                <th className="table-column-480 px-4 py-3 text-left w-60 text-[#887663] text-sm font-medium leading-normal">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              {menuItems.map((item) => (
                <tr key={item.id} className="border-t border-t-[#e5e1dc]">
                  <td className="table-column-120 h-[72px] px-4 py-2 w-[400px] text-[#181511] text-sm font-normal leading-normal">
                    {item.name}
                  </td>
                  <td className="table-column-240 h-[72px] px-4 py-2 w-[400px] text-[#887663] text-sm font-normal leading-normal">
                    {item.description}
                  </td>
                  <td className="table-column-360 h-[72px] px-4 py-2 w-[400px] text-[#887663] text-sm font-normal leading-normal">${item.price.toFixed(2)}</td>
                  <td className="table-column-480 h-[72px] px-4 py-2 w-60 text-[#887663] text-sm font-bold leading-normal tracking-[0.015em] cursor-pointer" onClick={() => handleEditItem(item.id)}>
                    Edit
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <style dangerouslySetInnerHTML={{
          __html: `
            @container(max-width:120px){.table-column-120{display: none;}}
            @container(max-width:240px){.table-column-240{display: none;}}
            @container(max-width:360px){.table-column-360{display: none;}}
            @container(max-width:480px){.table-column-480{display: none;}}
          `
        }} />
      </div>

      <div className="flex px-4 py-3 justify-end">
        <button
          onClick={handlePublishMenu}
          disabled={isPublishing}
          className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#e58219] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em] disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span className="truncate">{isPublishing ? 'Publishing...' : 'Publish Menu'}</span>
        </button>
      </div>

      {/* Edit Item Modal */}
      <EditItemModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        item={currentEditItem}
        onSave={handleSaveEditedItem}
      />
    </>
  );
}
