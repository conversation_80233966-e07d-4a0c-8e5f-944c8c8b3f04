'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>le, Di<PERSON>Footer } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  image?: string;
}

interface EditItemModalProps {
  isOpen: boolean;
  onClose: () => void;
  item: MenuItem | null;
  onSave: (item: MenuItem) => void;
}

export default function EditItemModal({ isOpen, onClose, item, onSave }: EditItemModalProps) {
  const [editedItem, setEditedItem] = useState<MenuItem | null>(null);

  useEffect(() => {
    if (item) {
      setEditedItem({ ...item });
    }
  }, [item]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (!editedItem) return;
    
    const { name, value } = e.target;
    
    if (name === 'price') {
      setEditedItem({
        ...editedItem,
        [name]: parseFloat(value) || 0,
      });
    } else {
      setEditedItem({
        ...editedItem,
        [name]: value,
      });
    }
  };

  const handleSave = () => {
    if (editedItem) {
      onSave(editedItem);
      onClose();
    }
  };

  if (!editedItem) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] bg-[#fbfaf9] border-[#e5e1dc]">
        <DialogHeader>
          <DialogTitle className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em]">Edit Menu Item</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-[#181511] text-sm font-medium">Name</Label>
            <Input
              id="name"
              name="name"
              value={editedItem.name}
              onChange={handleChange}
              className="col-span-3 bg-white border-[#e5e1dc] text-[#181511]"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="description" className="text-[#181511] text-sm font-medium">Description</Label>
            <Textarea
              id="description"
              name="description"
              value={editedItem.description}
              onChange={handleChange}
              className="col-span-3 bg-white border-[#e5e1dc] text-[#181511]"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="price" className="text-[#181511] text-sm font-medium">Price</Label>
            <Input
              id="price"
              name="price"
              type="number"
              step="0.01"
              value={editedItem.price}
              onChange={handleChange}
              className="col-span-3 bg-white border-[#e5e1dc] text-[#181511]"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="category" className="text-[#181511] text-sm font-medium">Category</Label>
            <select
              id="category"
              name="category"
              value={editedItem.category}
              onChange={(e) => setEditedItem({ ...editedItem, category: e.target.value })}
              className="col-span-3 bg-white border-[#e5e1dc] text-[#181511] rounded-md h-10 px-3"
            >
              <option value="Appetizer">Appetizer</option>
              <option value="Main Course">Main Course</option>
              <option value="Dessert">Dessert</option>
              <option value="Beverage">Beverage</option>
            </select>
          </div>
        </div>
        <DialogFooter>
          <Button 
            type="button" 
            variant="outline" 
            onClick={onClose}
            className="bg-[#f4f2f0] text-[#181511] border-[#e5e1dc] hover:bg-[#e5e1dc] hover:text-[#181511]"
          >
            Cancel
          </Button>
          <Button 
            type="button" 
            onClick={handleSave}
            className="bg-[#e58219] text-[#181511] hover:bg-[#d67616]"
          >
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
