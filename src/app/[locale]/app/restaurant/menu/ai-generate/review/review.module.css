/* Review page specific styles */
.reviewContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.menuPreview {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.menuImage {
  width: 100%;
  aspect-ratio: 16/9;
  background-size: cover;
  background-position: center;
  border-radius: 0.75rem;
}

.menuInfo {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.menuTitle {
  font-size: 1.25rem;
  font-weight: 700;
  color: #181511;
}

.menuDescription {
  font-size: 0.875rem;
  color: #887663;
}

.itemsTable {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 0.75rem;
  overflow: hidden;
  border: 1px solid #e5e1dc;
}

.tableHeader {
  background-color: #fbfaf9;
  text-align: left;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #181511;
}

.tableCell {
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  border-top: 1px solid #e5e1dc;
}

.itemName {
  font-weight: 500;
  color: #181511;
}

.itemDescription, .itemPrice {
  color: #887663;
}

.actionButton {
  font-weight: 600;
  color: #887663;
  cursor: pointer;
}

.actionButton:hover {
  color: #e58219;
}

.publishButton {
  background-color: #e58219;
  color: #181511;
  font-weight: 600;
  padding: 0.5rem 1.5rem;
  border-radius: 0.75rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.publishButton:hover {
  background-color: #d67616;
}

.publishButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive styles */
@media (min-width: 768px) {
  .menuPreview {
    flex-direction: row;
    align-items: flex-start;
  }
  
  .menuImage {
    width: 40%;
  }
  
  .menuInfo {
    width: 60%;
    padding-left: 1.5rem;
  }
}
