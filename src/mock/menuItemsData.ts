export interface MenuItem {
  id: string;
  name: string;
  category: 'Appetizers' | 'Main Courses' | 'Desserts' | 'Drinks';
  description: string;
  price: number;
  image: string;
  available: boolean;
}

export const menuItems: MenuItem[] = [
  {
    id: '1',
    name: 'Margherita Pizza',
    category: 'Main Courses',
    description: 'Classic pizza with fresh basil, mozzarella, and tomato sauce.',
    price: 12.99,
    image: '/images/menu/margherita-pizza.jpg',
    available: true
  },
  {
    id: '2',
    name: 'Caesar Salad',
    category: 'Appetizers',
    description: 'Romaine lettuce, croutons, Parmesan cheese, and Caesar dressing.',
    price: 9.99,
    image: '/images/menu/caesar-salad.jpg',
    available: true
  },
  {
    id: '3',
    name: 'Grilled Ribeye Steak',
    category: 'Main Courses',
    description: '12 oz ribeye steak grilled to perfection.',
    price: 24.99,
    image: '/images/menu/ribeye-steak.jpg',
    available: true
  },
  {
    id: '4',
    name: 'Chocolate Lava Cake',
    category: 'Desserts',
    description: 'Warm chocolate cake with a molten chocolate center.',
    price: 7.99,
    image: '/images/menu/chocolate-lava-cake.jpg',
    available: true
  },
  {
    id: '5',
    name: 'Spaghetti Carbonara',
    category: 'Main Courses',
    description: 'Spaghetti with a creamy sauce, pancetta, and Parmesan cheese.',
    price: 14.99,
    image: '/images/menu/spaghetti-carbonara.jpg',
    available: true
  },
  {
    id: '6',
    name: 'Bruschetta',
    category: 'Appetizers',
    description: 'Toasted bread topped with diced tomatoes, fresh basil, and garlic.',
    price: 8.99,
    image: '/images/menu/bruschetta.jpg',
    available: true
  },
  {
    id: '7',
    name: 'Tiramisu',
    category: 'Desserts',
    description: 'Classic Italian dessert with layers of coffee-soaked ladyfingers and mascarpone cream.',
    price: 6.99,
    image: '/images/menu/tiramisu.jpg',
    available: true
  },
  {
    id: '8',
    name: 'Chicken Alfredo',
    category: 'Main Courses',
    description: 'Fettuccine pasta with creamy Alfredo sauce and grilled chicken breast.',
    price: 16.99,
    image: '/images/menu/chicken-alfredo.jpg',
    available: true
  },
  {
    id: '9',
    name: 'Mozzarella Sticks',
    category: 'Appetizers',
    description: 'Breaded and fried mozzarella cheese sticks served with marinara sauce.',
    price: 7.99,
    image: '/images/menu/mozzarella-sticks.jpg',
    available: false
  },
  {
    id: '10',
    name: 'New York Cheesecake',
    category: 'Desserts',
    description: 'Creamy cheesecake with a graham cracker crust, topped with fresh berries.',
    price: 8.99,
    image: '/images/menu/cheesecake.jpg',
    available: true
  },
  {
    id: '11',
    name: 'Garlic Bread',
    category: 'Appetizers',
    description: 'Toasted bread with garlic butter and herbs.',
    price: 4.99,
    image: '/images/menu/garlic-bread.jpg',
    available: true
  },
  {
    id: '12',
    name: 'Salmon Fillet',
    category: 'Main Courses',
    description: 'Grilled salmon fillet with lemon butter sauce and seasonal vegetables.',
    price: 19.99,
    image: '/images/menu/salmon-fillet.jpg',
    available: true
  }
];

export default menuItems;
